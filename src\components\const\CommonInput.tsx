'use client';

import React, { forwardRef } from 'react';
import {
  TextField,
  InputAdornment,
  IconButton,
  useTheme,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { CommonInputProps, InputType } from '../../models/components';

const CommonInput = forwardRef<HTMLInputElement, CommonInputProps>(
  (
    {
      name,
      label,
      placeholder,
      value,
      onChange,
      type = InputType.TEXT,
      variant = 'outlined',
      size = 'medium',
      multiline = false,
      rows,
      maxRows,
      required = false,
      disabled = false,
      readOnly = false,
      error = false,
      helperText,
      fullWidth = true,
      autoFocus = false,
      autoComplete,
      startIcon,
      endIcon,
      startAdornment,
      endAdornment,
      onBlur,
      onFocus,
      onKeyDown,
      onKeyUp,
      inputProps,
      InputProps,
      InputLabelProps,
      FormHelperTextProps,
      sx,
      id,
      className,
      testId,
      ...rest
    },
    ref
  ) => {
    const theme = useTheme();
    const [showPassword, setShowPassword] = React.useState(false);

    // Handle password visibility toggle
    const handleTogglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    // Determine the actual input type
    const getInputType = () => {
      if (type === InputType.PASSWORD) {
        return showPassword ? 'text' : 'password';
      }
      return type;
    };

    // Build start adornment
    const buildStartAdornment = () => {
      if (startAdornment) return startAdornment;
      if (startIcon) {
        return (
          <InputAdornment position="start">
            {startIcon}
          </InputAdornment>
        );
      }
      return undefined;
    };

    // Build end adornment
    const buildEndAdornment = () => {
      const elements = [];

      // Add password visibility toggle for password fields
      if (type === InputType.PASSWORD) {
        elements.push(
          <IconButton
            key="password-toggle"
            aria-label="toggle password visibility"
            onClick={handleTogglePasswordVisibility}
            edge="end"
            size="small"
          >
            {showPassword ? <VisibilityOff /> : <Visibility />}
          </IconButton>
        );
      }

      // Add end icon if provided
      if (endIcon) {
        elements.push(
          <React.Fragment key="end-icon">
            {endIcon}
          </React.Fragment>
        );
      }

      // Add custom end adornment if provided
      if (endAdornment) {
        elements.push(
          <React.Fragment key="end-adornment">
            {endAdornment}
          </React.Fragment>
        );
      }

      // Return InputAdornment if we have elements
      if (elements.length > 0) {
        return (
          <InputAdornment position="end">
            {elements}
          </InputAdornment>
        );
      }

      return undefined;
    };

    return (
      <TextField
        ref={ref}
        id={id || name}
        name={name}
        label={label}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        type={getInputType()}
        variant={variant}
        size={size}
        multiline={multiline}
        rows={rows}
        maxRows={maxRows}
        required={required}
        disabled={disabled}
        error={error}
        helperText={helperText}
        fullWidth={fullWidth}
        autoFocus={autoFocus}
        autoComplete={autoComplete}
        onBlur={onBlur}
        onFocus={onFocus}
        onKeyDown={onKeyDown}
        onKeyUp={onKeyUp}
        className={className}
        sx={{
          '& .MuiOutlinedInput-root': {
            borderRadius: theme.shape.borderRadius,
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: theme.palette.primary.main,
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: theme.palette.primary.main,
              borderWidth: 2,
            },
          },
          '& .MuiInputLabel-root': {
            '&.Mui-focused': {
              color: theme.palette.primary.main,
            },
          },
          ...sx,
        }}
        slotProps={{
          input: {
            startAdornment: buildStartAdornment(),
            endAdornment: buildEndAdornment(),
            readOnly,
            ...inputProps,
            ...InputProps,
          },
          inputLabel: InputLabelProps,
          formHelperText: FormHelperTextProps,
        }}
        data-testid={testId}
        {...rest}
      />
    );
  }
);

CommonInput.displayName = 'CommonInput';

export default CommonInput;
