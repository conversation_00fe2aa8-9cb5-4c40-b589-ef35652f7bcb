'use client';

import React from 'react';
import { Box, Typography, Button, useTheme } from '@mui/material';

export default function TestTheme() {
  const theme = useTheme();
  
  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h4" gutterBottom>
        Theme Test Page
      </Typography>
      <Typography variant="body1" sx={{ mb: 2 }}>
        Primary color: {theme.palette.primary.main}
      </Typography>
      <Button variant="contained" color="primary">
        Test Button
      </Button>
    </Box>
  );
}
