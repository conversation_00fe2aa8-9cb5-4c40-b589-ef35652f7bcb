'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Con<PERSON>er,
  Al<PERSON>,
  Divider,
  <PERSON>,
  useTheme,
} from '@mui/material';
import {
  Email,
  Lock,
  Google,
  GitHub,
} from '@mui/icons-material';
import { commonColors, shadows } from '../lib/colors';
import { CommonInput, CommonButton } from '../components/const';
import { InputType, ButtonVariant, ButtonColor } from '../components/const';
import { LoginFormData, FormErrors } from '../models/auth';

const Login = () => {
  const theme = useTheme();
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState('');

  // Validation function
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes
  const handleInputChange = (field: keyof LoginFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined,
      }));
    }

    // Clear login error
    if (loginError) {
      setLoginError('');
    }
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setLoginError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // For demo purposes, show success
      console.log('Login successful:', formData);

      // Here you would typically handle successful login
      // e.g., redirect to dashboard, store auth token, etc.

    } catch (error) {
      setLoginError('Invalid email or password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle social login
  const handleSocialLogin = (provider: string) => {
    console.log(`Login with ${provider}`);
    // Implement social login logic here
  };

  return (
    <Container
      component="main"
      maxWidth="sm"
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 4,
      }}
    >
      <Card
        elevation={0}
        sx={{
          width: '100%',
          maxWidth: 400,
          boxShadow: shadows.card,
          borderRadius: 3,
        }}
      >
        <CardContent sx={{ p: 4 }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography
              variant="h4"
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: theme.palette.text.primary,
              }}
            >
              Welcome Back
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 2 }}
            >
              Sign in to your account to continue
            </Typography>
          </Box>

          {/* Login Error Alert */}
          {loginError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {loginError}
            </Alert>
          )}

          {/* Login Form */}
          <Box component="form" onSubmit={handleSubmit} noValidate>
            {/* Email Field */}
            <CommonInput
              name="email"
              label="Email Address"
              type={InputType.EMAIL}
              autoComplete="email"
              autoFocus
              value={formData.email}
              onChange={handleInputChange('email')}
              error={!!errors.email}
              helperText={errors.email}
              startIcon={<Email color="action" />}
              sx={{ mb: 2 }}
            />

            {/* Password Field */}
            <CommonInput
              name="password"
              label="Password"
              type={InputType.PASSWORD}
              autoComplete="current-password"
              value={formData.password}
              onChange={handleInputChange('password')}
              error={!!errors.password}
              helperText={errors.password}
              startIcon={<Lock color="action" />}
              sx={{ mb: 3 }}
            />

            {/* Forgot Password Link */}
            <Box sx={{ textAlign: 'right', mb: 3 }}>
              <Link
                component="button"
                variant="body2"
                onClick={() => window.location.href = '/forgot-password'}
                sx={{
                  color: theme.palette.primary.main,
                  textDecoration: 'none',
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                Forgot password?
              </Link>
            </Box>

            {/* Submit Button */}
            <CommonButton
              type="submit"
              fullWidth
              variant={ButtonVariant.CONTAINED}
              color={ButtonColor.PRIMARY}
              loading={isLoading}
              gradient
              shadow
              sx={{ mb: 3 }}
            >
              Sign In
            </CommonButton>

            {/* Divider */}
            <Divider sx={{ mb: 3 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            {/* Social Login Buttons */}
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <CommonButton
                fullWidth
                variant={ButtonVariant.OUTLINED}
                startIcon={<Google />}
                onClick={() => handleSocialLogin('Google')}
                sx={{
                  py: 1.5,
                  borderColor: commonColors.neutral.grey300,
                  color: commonColors.text.primary,
                  '&:hover': {
                    borderColor: commonColors.neutral.grey400,
                    backgroundColor: commonColors.neutral.grey50,
                  },
                }}
              >
                Google
              </CommonButton>
              <CommonButton
                fullWidth
                variant={ButtonVariant.OUTLINED}
                startIcon={<GitHub />}
                onClick={() => handleSocialLogin('GitHub')}
                sx={{
                  py: 1.5,
                  borderColor: commonColors.neutral.grey300,
                  color: commonColors.text.primary,
                  '&:hover': {
                    borderColor: commonColors.neutral.grey400,
                    backgroundColor: commonColors.neutral.grey50,
                  },
                }}
              >
                GitHub
              </CommonButton>
            </Box>

            {/* Sign Up Link */}
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Don't have an account?{' '}
                <Link
                  component="button"
                  onClick={() => window.location.href = '/signup'}
                  sx={{
                    color: theme.palette.primary.main,
                    textDecoration: 'none',
                    fontWeight: 500,
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  Sign up
                </Link>
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default Login;