'use client';

// Common color utilities and constants
export const commonColors = {
  // Brand colors
  brand: {
    primary: '#1976d2',
    secondary: '#dc004e',
    accent: '#ff6b35',
  },
  
  // Status colors
  status: {
    success: '#2e7d32',
    error: '#d32f2f',
    warning: '#ed6c02',
    info: '#0288d1',
  },
  
  // Neutral colors
  neutral: {
    white: '#ffffff',
    black: '#000000',
    grey50: '#fafafa',
    grey100: '#f5f5f5',
    grey200: '#eeeeee',
    grey300: '#e0e0e0',
    grey400: '#bdbdbd',
    grey500: '#9e9e9e',
    grey600: '#757575',
    grey700: '#616161',
    grey800: '#424242',
    grey900: '#212121',
  },
  
  // Background colors
  background: {
    light: '#fafafa',
    paper: '#ffffff',
    dark: '#121212',
    darkPaper: '#1e1e1e',
  },
  
  // Text colors
  text: {
    primary: 'rgba(0, 0, 0, 0.87)',
    secondary: 'rgba(0, 0, 0, 0.6)',
    disabled: 'rgba(0, 0, 0, 0.38)',
    primaryDark: 'rgba(255, 255, 255, 0.87)',
    secondaryDark: 'rgba(255, 255, 255, 0.6)',
    disabledDark: 'rgba(255, 255, 255, 0.38)',
  },
  
  // Gradient colors
  gradients: {
    primary: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
    secondary: 'linear-gradient(135deg, #dc004e 0%, #ff5983 100%)',
    success: 'linear-gradient(135deg, #2e7d32 0%, #4caf50 100%)',
    sunset: 'linear-gradient(135deg, #ff6b35 0%, #f7931e 100%)',
    ocean: 'linear-gradient(135deg, #0288d1 0%, #03a9f4 100%)',
  },
};

// Color utility functions
export const colorUtils = {
  // Add alpha to hex color
  addAlpha: (hexColor: string, alpha: number): string => {
    const hex = hexColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  },
  
  // Lighten a hex color
  lighten: (hexColor: string, amount: number): string => {
    const hex = hexColor.replace('#', '');
    const r = Math.min(255, parseInt(hex.substring(0, 2), 16) + amount);
    const g = Math.min(255, parseInt(hex.substring(2, 4), 16) + amount);
    const b = Math.min(255, parseInt(hex.substring(4, 6), 16) + amount);
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  },
  
  // Darken a hex color
  darken: (hexColor: string, amount: number): string => {
    const hex = hexColor.replace('#', '');
    const r = Math.max(0, parseInt(hex.substring(0, 2), 16) - amount);
    const g = Math.max(0, parseInt(hex.substring(2, 4), 16) - amount);
    const b = Math.max(0, parseInt(hex.substring(4, 6), 16) - amount);
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  },
  
  // Get contrast color (black or white) for a given background
  getContrastColor: (hexColor: string): string => {
    const hex = hexColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  },
};

// Common shadow utilities
export const shadows = {
  light: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
  medium: '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
  heavy: '0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)',
  card: '0 2px 8px rgba(0,0,0,0.1)',
  button: '0 2px 4px rgba(0,0,0,0.1)',
  buttonHover: '0 4px 8px rgba(0,0,0,0.15)',
};

// Common border radius values
export const borderRadius = {
  small: '4px',
  medium: '8px',
  large: '12px',
  round: '50%',
};

// Common spacing values (in pixels)
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export default commonColors;
