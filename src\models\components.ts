import { SxProps, Theme } from '@mui/material/styles';
import { TextFieldProps, ButtonProps } from '@mui/material';
import React from 'react';

// Base component props
export interface BaseComponentProps {
  id?: string;
  className?: string;
  sx?: SxProps<Theme>;
  testId?: string;
}

// Input component types
export enum InputType {
  TEXT = 'text',
  EMAIL = 'email',
  PASSWORD = 'password',
  NUMBER = 'number',
  TEL = 'tel',
  URL = 'url',
  SEARCH = 'search',
  DATE = 'date',
  TIME = 'time',
  DATETIME_LOCAL = 'datetime-local',
}

export enum InputVariant {
  OUTLINED = 'outlined',
  FILLED = 'filled',
  STANDARD = 'standard',
}

export enum InputSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

export interface CommonInputProps extends BaseComponentProps {
  // Basic props
  name: string;
  label?: string;
  placeholder?: string;
  value: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  
  // Input type and behavior
  type?: InputType;
  variant?: InputVariant;
  size?: InputSize;
  multiline?: boolean;
  rows?: number;
  maxRows?: number;
  
  // Validation and state
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  error?: boolean;
  helperText?: string;
  
  // Visual enhancements
  fullWidth?: boolean;
  autoFocus?: boolean;
  autoComplete?: string;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  
  // Event handlers
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onKeyUp?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  
  // Advanced props
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  InputProps?: Partial<TextFieldProps['InputProps']>;
  InputLabelProps?: Partial<TextFieldProps['InputLabelProps']>;
  FormHelperTextProps?: Partial<TextFieldProps['FormHelperTextProps']>;
}

// Button component types
export enum ButtonVariant {
  CONTAINED = 'contained',
  OUTLINED = 'outlined',
  TEXT = 'text',
}

export enum ButtonColor {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
  INHERIT = 'inherit',
}

export enum ButtonSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

export interface CommonButtonProps extends BaseComponentProps {
  // Basic props
  children: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  
  // Button type and behavior
  type?: 'button' | 'submit' | 'reset';
  variant?: ButtonVariant;
  color?: ButtonColor;
  size?: ButtonSize;
  
  // State
  disabled?: boolean;
  loading?: boolean;
  
  // Visual enhancements
  fullWidth?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  
  // Advanced props
  href?: string;
  target?: string;
  rel?: string;
  component?: React.ElementType;
  
  // Event handlers
  onMouseEnter?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  onMouseLeave?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLButtonElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLButtonElement>) => void;
  
  // Custom styling
  gradient?: boolean;
  shadow?: boolean;
  rounded?: boolean;
}

// Form component types
export interface FormFieldConfig {
  name: string;
  label: string;
  type: InputType;
  required?: boolean;
  validation?: ValidationRule[];
  placeholder?: string;
  helperText?: string;
}

export interface ValidationRule {
  type: 'required' | 'email' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: string) => boolean;
}

// Layout and container types
export interface ContainerProps extends BaseComponentProps {
  children: React.ReactNode;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  fixed?: boolean;
  disableGutters?: boolean;
}

export interface CardProps extends BaseComponentProps {
  children: React.ReactNode;
  elevation?: number;
  variant?: 'elevation' | 'outlined';
  raised?: boolean;
}

// Loading and state types
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface ErrorState {
  hasError: boolean;
  message?: string;
  code?: string;
}

// Theme and styling types
export interface ThemeColors {
  primary: string;
  secondary: string;
  success: string;
  error: string;
  warning: string;
  info: string;
}

export interface ComponentTheme {
  colors: ThemeColors;
  spacing: number;
  borderRadius: number;
  shadows: string[];
}
