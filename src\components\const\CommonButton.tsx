'use client';

import React, { forwardRef } from 'react';
import {
  Button,
  CircularProgress,
  useTheme,
  alpha,
} from '@mui/material';
import { CommonButtonProps, ButtonVariant, ButtonColor, ButtonSize } from '../../models/components';
import { commonColors, shadows } from '../../lib/colors';

const CommonButton = forwardRef<HTMLButtonElement, CommonButtonProps>(
  (
    {
      children,
      onClick,
      type = 'button',
      variant = ButtonVariant.CONTAINED,
      color = ButtonColor.PRIMARY,
      size = ButtonSize.MEDIUM,
      disabled = false,
      loading = false,
      fullWidth = false,
      startIcon,
      endIcon,
      href,
      target,
      rel,
      component,
      onMouseEnter,
      onMouseLeave,
      onFocus,
      onBlur,
      gradient = false,
      shadow = false,
      rounded = false,
      sx,
      id,
      className,
      testId,
      ...rest
    },
    ref
  ) => {
    const theme = useTheme();

    // Get gradient background based on color
    const getGradientBackground = () => {
      switch (color) {
        case ButtonColor.PRIMARY:
          return commonColors.gradients.primary;
        case ButtonColor.SECONDARY:
          return commonColors.gradients.secondary;
        case ButtonColor.SUCCESS:
          return commonColors.gradients.success;
        case ButtonColor.ERROR:
          return 'linear-gradient(135deg, #d32f2f 0%, #f44336 100%)';
        case ButtonColor.WARNING:
          return 'linear-gradient(135deg, #ed6c02 0%, #ff9800 100%)';
        case ButtonColor.INFO:
          return commonColors.gradients.ocean;
        default:
          return commonColors.gradients.primary;
      }
    };

    // Get shadow style
    const getShadowStyle = () => {
      if (!shadow) return {};
      
      return {
        boxShadow: shadows.button,
        '&:hover': {
          boxShadow: shadows.buttonHover,
        },
      };
    };

    // Get border radius
    const getBorderRadius = () => {
      if (rounded) return '50px';
      return theme.shape.borderRadius;
    };

    // Build custom styles
    const getCustomStyles = () => {
      const styles: any = {
        borderRadius: getBorderRadius(),
        textTransform: 'none',
        fontWeight: 500,
        ...getShadowStyle(),
      };

      // Apply gradient if requested and variant is contained
      if (gradient && variant === ButtonVariant.CONTAINED) {
        styles.background = getGradientBackground();
        styles.border = 'none';
        styles['&:hover'] = {
          background: getGradientBackground(),
          opacity: 0.9,
          ...styles['&:hover'],
        };
        styles['&:disabled'] = {
          background: alpha(theme.palette.action.disabled, 0.12),
          color: theme.palette.action.disabled,
        };
      }

      // Custom size padding
      if (size === ButtonSize.LARGE) {
        styles.padding = '12px 24px';
        styles.fontSize = '1.1rem';
      } else if (size === ButtonSize.SMALL) {
        styles.padding = '6px 12px';
        styles.fontSize = '0.875rem';
      } else {
        styles.padding = '8px 16px';
        styles.fontSize = '1rem';
      }

      return styles;
    };

    // Handle loading state
    const isDisabled = disabled || loading;
    const buttonStartIcon = loading ? (
      <CircularProgress
        size={16}
        color="inherit"
        sx={{ mr: 0.5 }}
      />
    ) : startIcon;

    return (
      <Button
        ref={ref}
        id={id}
        type={type}
        variant={variant}
        color={color}
        size={size}
        disabled={isDisabled}
        fullWidth={fullWidth}
        startIcon={buttonStartIcon}
        endIcon={!loading ? endIcon : undefined}
        href={href}
        target={target}
        rel={rel}
        component={component}
        onClick={onClick}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        onFocus={onFocus}
        onBlur={onBlur}
        className={className}
        data-testid={testId}
        sx={{
          ...getCustomStyles(),
          ...sx,
        }}
        {...rest}
      >
        {loading ? 'Loading...' : children}
      </Button>
    );
  }
);

CommonButton.displayName = 'CommonButton';

export default CommonButton;
