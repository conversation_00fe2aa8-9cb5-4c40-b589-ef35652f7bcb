'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Container,
  Alert,
  Link,
  useTheme,
} from '@mui/material';
import {
  Email,
  ArrowBack,
  CheckCircle,
} from '@mui/icons-material';
import { commonColors, shadows } from '../../lib/colors';
import { CommonInput, CommonButton } from '../../components/const';
import { InputType, ButtonVariant, ButtonColor } from '../../components/const';
import { ForgotPasswordFormData, FormErrors, FormSubmissionState } from '../../models/auth';

const ForgotPassword = () => {
  const theme = useTheme();
  const [formData, setFormData] = useState<ForgotPasswordFormData>({
    email: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [submissionState, setSubmissionState] = useState<FormSubmissionState>({
    isSubmitting: false,
    isSuccess: false,
    error: null,
    successMessage: null,
  });

  // Validation function
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setFormData({ email: value });
    
    // Clear error when user starts typing
    if (errors.email) {
      setErrors({});
    }
    
    // Clear submission error
    if (submissionState.error) {
      setSubmissionState(prev => ({
        ...prev,
        error: null,
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSubmissionState({
      isSubmitting: true,
      isSuccess: false,
      error: null,
      successMessage: null,
    });

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // For demo purposes, show success
      console.log('Password reset email sent to:', formData.email);
      
      setSubmissionState({
        isSubmitting: false,
        isSuccess: true,
        error: null,
        successMessage: `Password reset instructions have been sent to ${formData.email}`,
      });
      
    } catch (error) {
      setSubmissionState({
        isSubmitting: false,
        isSuccess: false,
        error: 'Failed to send reset email. Please try again.',
        successMessage: null,
      });
    }
  };

  // Handle back to login
  const handleBackToLogin = () => {
    // In a real app, you would use Next.js router
    console.log('Navigate back to login');
    window.location.href = '/';
  };

  return (
    <Container
      component="main"
      maxWidth="sm"
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 4,
      }}
    >
      <Card
        elevation={0}
        sx={{
          width: '100%',
          maxWidth: 400,
          boxShadow: shadows.card,
          borderRadius: 3,
        }}
      >
        <CardContent sx={{ p: 4 }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            {submissionState.isSuccess ? (
              <>
                <CheckCircle
                  sx={{
                    fontSize: 64,
                    color: theme.palette.success.main,
                    mb: 2,
                  }}
                />
                <Typography
                  variant="h4"
                  component="h1"
                  gutterBottom
                  sx={{
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                  }}
                >
                  Check Your Email
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mb: 2 }}
                >
                  We've sent password reset instructions to your email address
                </Typography>
              </>
            ) : (
              <>
                <Typography
                  variant="h4"
                  component="h1"
                  gutterBottom
                  sx={{
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                  }}
                >
                  Forgot Password?
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mb: 2 }}
                >
                  Enter your email address and we'll send you instructions to reset your password
                </Typography>
              </>
            )}
          </Box>

          {/* Success Message */}
          {submissionState.successMessage && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {submissionState.successMessage}
            </Alert>
          )}

          {/* Error Message */}
          {submissionState.error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {submissionState.error}
            </Alert>
          )}

          {!submissionState.isSuccess ? (
            <>
              {/* Forgot Password Form */}
              <Box component="form" onSubmit={handleSubmit} noValidate>
                {/* Email Field */}
                <CommonInput
                  name="email"
                  label="Email Address"
                  type={InputType.EMAIL}
                  autoComplete="email"
                  autoFocus
                  value={formData.email}
                  onChange={handleInputChange}
                  error={!!errors.email}
                  helperText={errors.email}
                  startIcon={<Email color="action" />}
                  placeholder="Enter your email address"
                  sx={{ mb: 3 }}
                />

                {/* Submit Button */}
                <CommonButton
                  type="submit"
                  fullWidth
                  variant={ButtonVariant.CONTAINED}
                  color={ButtonColor.PRIMARY}
                  loading={submissionState.isSubmitting}
                  gradient
                  shadow
                  sx={{ mb: 3 }}
                >
                  Send Reset Instructions
                </CommonButton>
              </Box>
            </>
          ) : (
            <>
              {/* Success Actions */}
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Didn't receive the email? Check your spam folder or try again.
                </Typography>
                <CommonButton
                  variant={ButtonVariant.OUTLINED}
                  color={ButtonColor.PRIMARY}
                  onClick={() => setSubmissionState({
                    isSubmitting: false,
                    isSuccess: false,
                    error: null,
                    successMessage: null,
                  })}
                  sx={{ mb: 2 }}
                >
                  Try Again
                </CommonButton>
              </Box>
            </>
          )}

          {/* Back to Login Link */}
          <Box sx={{ textAlign: 'center' }}>
            <Link
              component="button"
              variant="body2"
              onClick={handleBackToLogin}
              sx={{
                color: theme.palette.primary.main,
                textDecoration: 'none',
                display: 'inline-flex',
                alignItems: 'center',
                gap: 0.5,
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              <ArrowBack fontSize="small" />
              Back to Login
            </Link>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default ForgotPassword;
