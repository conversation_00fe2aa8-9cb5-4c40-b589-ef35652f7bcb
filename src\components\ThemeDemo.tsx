'use client';

import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  useTheme,
  Grid,
} from '@mui/material';
import { commonColors, shadows, borderRadius } from '../lib/colors';

const ThemeDemo = () => {
  const theme = useTheme();

  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h4" gutterBottom>
        Theme Demo
      </Typography>
      
      {/* Color Palette */}
      <Card sx={{ mb: 4, boxShadow: shadows.card }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Color Palette
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  bgcolor: theme.palette.primary.main,
                  color: theme.palette.primary.contrastText,
                  p: 2,
                  borderRadius: borderRadius.medium,
                  textAlign: 'center',
                }}
              >
                Primary
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  bgcolor: theme.palette.secondary.main,
                  color: theme.palette.secondary.contrastText,
                  p: 2,
                  borderRadius: borderRadius.medium,
                  textAlign: 'center',
                }}
              >
                Secondary
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  bgcolor: theme.palette.success.main,
                  color: theme.palette.success.contrastText,
                  p: 2,
                  borderRadius: borderRadius.medium,
                  textAlign: 'center',
                }}
              >
                Success
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  bgcolor: theme.palette.error.main,
                  color: theme.palette.error.contrastText,
                  p: 2,
                  borderRadius: borderRadius.medium,
                  textAlign: 'center',
                }}
              >
                Error
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Buttons */}
      <Card sx={{ mb: 4, boxShadow: shadows.card }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Buttons
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button variant="contained" color="primary">
              Primary
            </Button>
            <Button variant="contained" color="secondary">
              Secondary
            </Button>
            <Button variant="outlined" color="primary">
              Outlined
            </Button>
            <Button variant="text" color="primary">
              Text
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Typography */}
      <Card sx={{ mb: 4, boxShadow: shadows.card }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Typography
          </Typography>
          <Typography variant="h1" gutterBottom>
            Heading 1
          </Typography>
          <Typography variant="h2" gutterBottom>
            Heading 2
          </Typography>
          <Typography variant="h3" gutterBottom>
            Heading 3
          </Typography>
          <Typography variant="body1" gutterBottom>
            Body 1: Lorem ipsum dolor sit amet, consectetur adipiscing elit.
          </Typography>
          <Typography variant="body2" gutterBottom>
            Body 2: Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </Typography>
        </CardContent>
      </Card>

      {/* Chips */}
      <Card sx={{ mb: 4, boxShadow: shadows.card }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Chips
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip label="Default" />
            <Chip label="Primary" color="primary" />
            <Chip label="Secondary" color="secondary" />
            <Chip label="Success" color="success" />
            <Chip label="Error" color="error" />
          </Box>
        </CardContent>
      </Card>

      {/* Gradients */}
      <Card sx={{ boxShadow: shadows.card }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Gradient Examples
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  background: commonColors.gradients.primary,
                  color: 'white',
                  p: 2,
                  borderRadius: borderRadius.medium,
                  textAlign: 'center',
                }}
              >
                Primary Gradient
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  background: commonColors.gradients.secondary,
                  color: 'white',
                  p: 2,
                  borderRadius: borderRadius.medium,
                  textAlign: 'center',
                }}
              >
                Secondary Gradient
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  background: commonColors.gradients.sunset,
                  color: 'white',
                  p: 2,
                  borderRadius: borderRadius.medium,
                  textAlign: 'center',
                }}
              >
                Sunset Gradient
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  background: commonColors.gradients.ocean,
                  color: 'white',
                  p: 2,
                  borderRadius: borderRadius.medium,
                  textAlign: 'center',
                }}
              >
                Ocean Gradient
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ThemeDemo;
