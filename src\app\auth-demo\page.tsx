'use client';

import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Container,
  Grid,
  useTheme,
} from '@mui/material';
import {
  Login,
  PersonAdd,
  LockReset,
  Home,
} from '@mui/icons-material';
import { CommonButton } from '../../components/const';
import { ButtonVariant, ButtonColor } from '../../components/const';
import { shadows } from '../../lib/colors';

const AuthDemo = () => {
  const theme = useTheme();

  const authPages = [
    {
      title: 'Login Page',
      description: 'User authentication with email and password, social login options, and forgot password link.',
      icon: <Login sx={{ fontSize: 48, color: theme.palette.primary.main }} />,
      path: '/',
      features: [
        'Email & Password validation',
        'Password visibility toggle',
        'Social login (Google, GitHub)',
        'Loading states',
        'Error handling',
        'Responsive design'
      ]
    },
    {
      title: 'Signup Page',
      description: 'Complete user registration with comprehensive form validation and terms acceptance.',
      icon: <PersonAdd sx={{ fontSize: 48, color: theme.palette.secondary.main }} />,
      path: '/signup',
      features: [
        'Multi-field validation',
        'Password strength checking',
        'Optional fields (phone, DOB)',
        'Terms & conditions',
        'Social signup options',
        'Grid layout for better UX'
      ]
    },
    {
      title: 'Forgot Password',
      description: 'Password reset functionality with email verification and success states.',
      icon: <LockReset sx={{ fontSize: 48, color: theme.palette.warning.main }} />,
      path: '/forgot-password',
      features: [
        'Email validation',
        'Success confirmation',
        'Retry functionality',
        'Clear user feedback',
        'Back to login navigation',
        'Professional messaging'
      ]
    }
  ];

  const handleNavigate = (path: string) => {
    window.location.href = path;
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography
          variant="h3"
          component="h1"
          gutterBottom
          sx={{ fontWeight: 600, color: theme.palette.text.primary }}
        >
          Authentication Pages Demo
        </Typography>
        <Typography
          variant="h6"
          color="text.secondary"
          sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}
        >
          Explore our complete authentication system built with Material-UI, TypeScript models, and reusable components.
        </Typography>
        
        <CommonButton
          variant={ButtonVariant.OUTLINED}
          startIcon={<Home />}
          onClick={() => handleNavigate('/')}
          sx={{ mb: 4 }}
        >
          Go to Login Page
        </CommonButton>
      </Box>

      {/* Auth Pages Grid */}
      <Grid container spacing={4}>
        {authPages.map((page, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                boxShadow: shadows.card,
                borderRadius: 3,
                transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: shadows.heavy,
                },
              }}
            >
              <CardContent sx={{ flexGrow: 1, p: 3 }}>
                {/* Icon */}
                <Box sx={{ textAlign: 'center', mb: 2 }}>
                  {page.icon}
                </Box>

                {/* Title */}
                <Typography
                  variant="h5"
                  component="h2"
                  gutterBottom
                  sx={{ fontWeight: 600, textAlign: 'center' }}
                >
                  {page.title}
                </Typography>

                {/* Description */}
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mb: 3, textAlign: 'center' }}
                >
                  {page.description}
                </Typography>

                {/* Features */}
                <Typography
                  variant="subtitle2"
                  sx={{ fontWeight: 600, mb: 1 }}
                >
                  Key Features:
                </Typography>
                <Box component="ul" sx={{ pl: 2, mb: 3 }}>
                  {page.features.map((feature, featureIndex) => (
                    <Typography
                      component="li"
                      variant="body2"
                      color="text.secondary"
                      key={featureIndex}
                      sx={{ mb: 0.5 }}
                    >
                      {feature}
                    </Typography>
                  ))}
                </Box>

                {/* Action Button */}
                <CommonButton
                  fullWidth
                  variant={ButtonVariant.CONTAINED}
                  color={index === 0 ? ButtonColor.PRIMARY : index === 1 ? ButtonColor.SECONDARY : ButtonColor.WARNING}
                  onClick={() => handleNavigate(page.path)}
                  gradient
                  shadow
                >
                  View {page.title}
                </CommonButton>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Technical Details */}
      <Box sx={{ mt: 6 }}>
        <Card sx={{ boxShadow: shadows.card, borderRadius: 3 }}>
          <CardContent sx={{ p: 4 }}>
            <Typography
              variant="h5"
              component="h2"
              gutterBottom
              sx={{ fontWeight: 600, textAlign: 'center', mb: 3 }}
            >
              Technical Implementation
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  Architecture Highlights
                </Typography>
                <Box component="ul" sx={{ pl: 2 }}>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    <strong>TypeScript Models:</strong> Organized in src/models/ for type safety
                  </Typography>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    <strong>Reusable Components:</strong> CommonInput and CommonButton with 20+ props
                  </Typography>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    <strong>Material-UI Integration:</strong> Custom theme with gradients and shadows
                  </Typography>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    <strong>Form Validation:</strong> Real-time validation with error handling
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  Component Features
                </Typography>
                <Box component="ul" sx={{ pl: 2 }}>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    <strong>CommonInput:</strong> Password toggle, icons, validation states
                  </Typography>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    <strong>CommonButton:</strong> Loading states, gradients, multiple variants
                  </Typography>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    <strong>Responsive Design:</strong> Mobile-first approach with Grid system
                  </Typography>
                  <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                    <strong>Accessibility:</strong> ARIA labels, keyboard navigation, screen readers
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default AuthDemo;
