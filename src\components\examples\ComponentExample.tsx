'use client';

import React, { useState } from 'react';
import { Box, Typography, Card, CardContent, Grid } from '@mui/material';
import { Search, Send, Save, Delete } from '@mui/icons-material';
import { CommonInput, CommonButton } from '../const';
import { 
  InputType, 
  InputVariant, 
  InputSize, 
  ButtonVariant, 
  ButtonColor, 
  ButtonSize 
} from '../const';

const ComponentExample = () => {
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };

  const handleButtonClick = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h4" gutterBottom>
        Common Components Example
      </Typography>

      {/* Input Examples */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            CommonInput Examples
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <CommonInput
                name="basic"
                label="Basic Input"
                value={inputValue}
                onChange={handleInputChange}
                placeholder="Enter some text..."
                sx={{ mb: 2 }}
              />
              
              <CommonInput
                name="email"
                label="Email Input"
                type={InputType.EMAIL}
                value=""
                onChange={() => {}}
                startIcon={<Search />}
                placeholder="Enter your email..."
                sx={{ mb: 2 }}
              />
              
              <CommonInput
                name="password"
                label="Password Input"
                type={InputType.PASSWORD}
                value=""
                onChange={() => {}}
                placeholder="Enter your password..."
                sx={{ mb: 2 }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <CommonInput
                name="filled"
                label="Filled Variant"
                variant={InputVariant.FILLED}
                value=""
                onChange={() => {}}
                sx={{ mb: 2 }}
              />
              
              <CommonInput
                name="small"
                label="Small Size"
                size={InputSize.SMALL}
                value=""
                onChange={() => {}}
                sx={{ mb: 2 }}
              />
              
              <CommonInput
                name="error"
                label="Error State"
                value=""
                onChange={() => {}}
                error
                helperText="This field has an error"
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Button Examples */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            CommonButton Examples
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Button Variants
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                <CommonButton variant={ButtonVariant.CONTAINED}>
                  Contained
                </CommonButton>
                <CommonButton variant={ButtonVariant.OUTLINED}>
                  Outlined
                </CommonButton>
                <CommonButton variant={ButtonVariant.TEXT}>
                  Text
                </CommonButton>
              </Box>

              <Typography variant="subtitle2" gutterBottom>
                Button Colors
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                <CommonButton color={ButtonColor.PRIMARY}>
                  Primary
                </CommonButton>
                <CommonButton color={ButtonColor.SECONDARY}>
                  Secondary
                </CommonButton>
                <CommonButton color={ButtonColor.SUCCESS}>
                  Success
                </CommonButton>
                <CommonButton color={ButtonColor.ERROR}>
                  Error
                </CommonButton>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Button Sizes
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap', alignItems: 'center' }}>
                <CommonButton size={ButtonSize.SMALL}>
                  Small
                </CommonButton>
                <CommonButton size={ButtonSize.MEDIUM}>
                  Medium
                </CommonButton>
                <CommonButton size={ButtonSize.LARGE}>
                  Large
                </CommonButton>
              </Box>

              <Typography variant="subtitle2" gutterBottom>
                Special Features
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                <CommonButton 
                  gradient 
                  shadow
                  startIcon={<Send />}
                >
                  Gradient
                </CommonButton>
                <CommonButton 
                  loading={loading}
                  onClick={handleButtonClick}
                  startIcon={<Save />}
                >
                  Loading
                </CommonButton>
                <CommonButton 
                  rounded
                  endIcon={<Delete />}
                  color={ButtonColor.ERROR}
                >
                  Rounded
                </CommonButton>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ComponentExample;
