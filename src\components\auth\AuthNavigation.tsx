'use client';

import React from 'react';
import { Box, Typography, Link, useTheme } from '@mui/material';
import { AuthPage } from '../../models/auth';

interface AuthNavigationProps {
  currentPage: AuthPage;
}

const AuthNavigation: React.FC<AuthNavigationProps> = ({ currentPage }) => {
  const theme = useTheme();

  const handleNavigation = (path: string) => {
    // In a real app, you would use Next.js router
    window.location.href = path;
  };

  const renderNavigationContent = () => {
    switch (currentPage) {
      case AuthPage.LOGIN:
        return (
          <>
            <Typography variant="body2" color="text.secondary">
              Don't have an account?{' '}
              <Link
                component="button"
                onClick={() => handleNavigation('/signup')}
                sx={{
                  color: theme.palette.primary.main,
                  textDecoration: 'none',
                  fontWeight: 500,
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                Sign up
              </Link>
            </Typography>
          </>
        );

      case AuthPage.SIGNUP:
        return (
          <>
            <Typography variant="body2" color="text.secondary">
              Already have an account?{' '}
              <Link
                component="button"
                onClick={() => handleNavigation('/')}
                sx={{
                  color: theme.palette.primary.main,
                  textDecoration: 'none',
                  fontWeight: 500,
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                Sign in
              </Link>
            </Typography>
          </>
        );

      case AuthPage.FORGOT_PASSWORD:
        return (
          <>
            <Typography variant="body2" color="text.secondary">
              Remember your password?{' '}
              <Link
                component="button"
                onClick={() => handleNavigation('/')}
                sx={{
                  color: theme.palette.primary.main,
                  textDecoration: 'none',
                  fontWeight: 500,
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                Back to Login
              </Link>
            </Typography>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ textAlign: 'center' }}>
      {renderNavigationContent()}
    </Box>
  );
};

export default AuthNavigation;
