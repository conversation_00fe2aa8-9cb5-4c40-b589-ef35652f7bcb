'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Con<PERSON>er,
  Al<PERSON>,
  Divider,
  <PERSON>,
  useTheme,
  FormControlLabel,
  Checkbox,
  Grid,
} from '@mui/material';
import {
  Person,
  Email,
  Lock,
  Phone,
  Google,
  GitHub,
  CalendarToday,
} from '@mui/icons-material';
import { commonColors, shadows } from '../../lib/colors';
import { CommonInput, CommonButton } from '../../components/const';
import { InputType, ButtonVariant, ButtonColor } from '../../components/const';
import { RegisterFormData, FormErrors, FormSubmissionState } from '../../models/auth';

const Signup = () => {
  const theme = useTheme();
  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phoneNumber: '',
    dateOfBirth: '',
    agreeToTerms: false,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [submissionState, setSubmissionState] = useState<FormSubmissionState>({
    isSubmitting: false,
    isSuccess: false,
    error: null,
    successMessage: null,
  });

  // Validation function
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // First name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    } else if (formData.firstName.trim().length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters';
    }

    // Last name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    } else if (formData.lastName.trim().length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters';
    }

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Phone number validation (optional)
    if (formData.phoneNumber && !/^\+?[\d\s\-\(\)]+$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    // Terms validation
    if (!formData.agreeToTerms) {
      newErrors.terms = 'You must agree to the terms and conditions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes
  const handleInputChange = (field: keyof RegisterFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined,
      }));
    }
    
    // Clear submission error
    if (submissionState.error) {
      setSubmissionState(prev => ({
        ...prev,
        error: null,
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSubmissionState({
      isSubmitting: true,
      isSuccess: false,
      error: null,
      successMessage: null,
    });

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // For demo purposes, show success
      console.log('Account created successfully:', formData);
      
      setSubmissionState({
        isSubmitting: false,
        isSuccess: true,
        error: null,
        successMessage: 'Account created successfully! Please check your email to verify your account.',
      });
      
    } catch (error) {
      setSubmissionState({
        isSubmitting: false,
        isSuccess: false,
        error: 'Failed to create account. Please try again.',
        successMessage: null,
      });
    }
  };

  // Handle social signup
  const handleSocialSignup = (provider: string) => {
    console.log(`Sign up with ${provider}`);
    // Implement social signup logic here
  };

  // Handle navigation to login
  const handleGoToLogin = () => {
    // In a real app, you would use Next.js router
    console.log('Navigate to login');
    window.location.href = '/';
  };

  return (
    <Container
      component="main"
      maxWidth="md"
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 4,
      }}
    >
      <Card
        elevation={0}
        sx={{
          width: '100%',
          maxWidth: 600,
          boxShadow: shadows.card,
          borderRadius: 3,
        }}
      >
        <CardContent sx={{ p: 4 }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography
              variant="h4"
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: theme.palette.text.primary,
              }}
            >
              Create Account
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 2 }}
            >
              Join us today and get started with your journey
            </Typography>
          </Box>

          {/* Success Message */}
          {submissionState.successMessage && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {submissionState.successMessage}
            </Alert>
          )}

          {/* Error Message */}
          {submissionState.error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {submissionState.error}
            </Alert>
          )}

          {!submissionState.isSuccess ? (
            <>
              {/* Social Signup Buttons */}
              <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
                <CommonButton
                  fullWidth
                  variant={ButtonVariant.OUTLINED}
                  startIcon={<Google />}
                  onClick={() => handleSocialSignup('Google')}
                  sx={{
                    py: 1.5,
                    borderColor: commonColors.neutral.grey300,
                    color: commonColors.text.primary,
                    '&:hover': {
                      borderColor: commonColors.neutral.grey400,
                      backgroundColor: commonColors.neutral.grey50,
                    },
                  }}
                >
                  Google
                </CommonButton>
                <CommonButton
                  fullWidth
                  variant={ButtonVariant.OUTLINED}
                  startIcon={<GitHub />}
                  onClick={() => handleSocialSignup('GitHub')}
                  sx={{
                    py: 1.5,
                    borderColor: commonColors.neutral.grey300,
                    color: commonColors.text.primary,
                    '&:hover': {
                      borderColor: commonColors.neutral.grey400,
                      backgroundColor: commonColors.neutral.grey50,
                    },
                  }}
                >
                  GitHub
                </CommonButton>
              </Box>

              {/* Divider */}
              <Divider sx={{ mb: 3 }}>
                <Typography variant="body2" color="text.secondary">
                  or sign up with email
                </Typography>
              </Divider>

              {/* Signup Form */}
              <Box component="form" onSubmit={handleSubmit} noValidate>
                <Grid container spacing={2}>
                  {/* First Name */}
                  <Grid item xs={12} sm={6}>
                    <CommonInput
                      name="firstName"
                      label="First Name"
                      value={formData.firstName}
                      onChange={handleInputChange('firstName')}
                      error={!!errors.firstName}
                      helperText={errors.firstName}
                      startIcon={<Person color="action" />}
                      required
                    />
                  </Grid>

                  {/* Last Name */}
                  <Grid item xs={12} sm={6}>
                    <CommonInput
                      name="lastName"
                      label="Last Name"
                      value={formData.lastName}
                      onChange={handleInputChange('lastName')}
                      error={!!errors.lastName}
                      helperText={errors.lastName}
                      startIcon={<Person color="action" />}
                      required
                    />
                  </Grid>

                  {/* Email */}
                  <Grid item xs={12}>
                    <CommonInput
                      name="email"
                      label="Email Address"
                      type={InputType.EMAIL}
                      autoComplete="email"
                      value={formData.email}
                      onChange={handleInputChange('email')}
                      error={!!errors.email}
                      helperText={errors.email}
                      startIcon={<Email color="action" />}
                      required
                    />
                  </Grid>

                  {/* Phone Number */}
                  <Grid item xs={12} sm={6}>
                    <CommonInput
                      name="phoneNumber"
                      label="Phone Number (Optional)"
                      type={InputType.TEL}
                      value={formData.phoneNumber || ''}
                      onChange={handleInputChange('phoneNumber')}
                      error={!!errors.phoneNumber}
                      helperText={errors.phoneNumber}
                      startIcon={<Phone color="action" />}
                      placeholder="+****************"
                    />
                  </Grid>

                  {/* Date of Birth */}
                  <Grid item xs={12} sm={6}>
                    <CommonInput
                      name="dateOfBirth"
                      label="Date of Birth (Optional)"
                      type={InputType.DATE}
                      value={formData.dateOfBirth || ''}
                      onChange={handleInputChange('dateOfBirth')}
                      error={!!errors.dateOfBirth}
                      helperText={errors.dateOfBirth}
                      startIcon={<CalendarToday color="action" />}
                    />
                  </Grid>

                  {/* Password */}
                  <Grid item xs={12} sm={6}>
                    <CommonInput
                      name="password"
                      label="Password"
                      type={InputType.PASSWORD}
                      autoComplete="new-password"
                      value={formData.password}
                      onChange={handleInputChange('password')}
                      error={!!errors.password}
                      helperText={errors.password}
                      startIcon={<Lock color="action" />}
                      required
                    />
                  </Grid>

                  {/* Confirm Password */}
                  <Grid item xs={12} sm={6}>
                    <CommonInput
                      name="confirmPassword"
                      label="Confirm Password"
                      type={InputType.PASSWORD}
                      autoComplete="new-password"
                      value={formData.confirmPassword}
                      onChange={handleInputChange('confirmPassword')}
                      error={!!errors.confirmPassword}
                      helperText={errors.confirmPassword}
                      startIcon={<Lock color="action" />}
                      required
                    />
                  </Grid>
                </Grid>

                {/* Terms and Conditions */}
                <Box sx={{ mt: 2, mb: 3 }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.agreeToTerms}
                        onChange={handleInputChange('agreeToTerms')}
                        color="primary"
                      />
                    }
                    label={
                      <Typography variant="body2" color="text.secondary">
                        I agree to the{' '}
                        <Link href="#" color="primary" sx={{ textDecoration: 'none' }}>
                          Terms of Service
                        </Link>{' '}
                        and{' '}
                        <Link href="#" color="primary" sx={{ textDecoration: 'none' }}>
                          Privacy Policy
                        </Link>
                      </Typography>
                    }
                  />
                  {errors.terms && (
                    <Typography variant="caption" color="error" sx={{ display: 'block', mt: 0.5 }}>
                      {errors.terms}
                    </Typography>
                  )}
                </Box>

                {/* Submit Button */}
                <CommonButton
                  type="submit"
                  fullWidth
                  variant={ButtonVariant.CONTAINED}
                  color={ButtonColor.PRIMARY}
                  loading={submissionState.isSubmitting}
                  gradient
                  shadow
                  sx={{ mb: 3 }}
                >
                  Create Account
                </CommonButton>
              </Box>
            </>
          ) : (
            <Box sx={{ textAlign: 'center' }}>
              <CommonButton
                variant={ButtonVariant.CONTAINED}
                color={ButtonColor.PRIMARY}
                onClick={handleGoToLogin}
                gradient
                shadow
              >
                Go to Login
              </CommonButton>
            </Box>
          )}

          {/* Login Link */}
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Already have an account?{' '}
              <Link
                component="button"
                onClick={handleGoToLogin}
                sx={{
                  color: theme.palette.primary.main,
                  textDecoration: 'none',
                  fontWeight: 500,
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                Sign in
              </Link>
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default Signup;
